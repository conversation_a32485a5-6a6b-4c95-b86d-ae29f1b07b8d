{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from \"@prisma/client\";\r\n\r\ndeclare global {\r\n  var prisma: PrismaClient | undefined;\r\n}\r\n\r\nexport const db = globalThis.prisma || new PrismaClient();\r\n\r\nif (process.env.NODE_ENV !== \"production\") {\r\n  globalThis.prisma = db;\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAMO,MAAM,KAAK,WAAW,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEvD,wCAA2C;IACzC,WAAW,MAAM,GAAG;AACtB", "debugId": null}}, {"offset": {"line": 99, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/app/api/completed/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\nimport { db } from \"@/lib/db\";\nimport { Resend } from \"resend\";\nimport { promises as fs } from \"fs\";\nimport path from \"path\";\n\nconst resend = new Resend(process.env.AUTH_RESEND_KEY);\nconst BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL || \"http://localhost:8000\";\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { entryId, videoUrl, status } = await request.json();\n\n    if (!entryId || !videoUrl || !status) {\n      return NextResponse.json(\n        { error: \"Missing required parameters: entryId, videoUrl, status\" },\n        { status: 400 }\n      );\n    }\n\n    console.log(\"🎉 Video processing completed:\", {\n      entryId,\n      videoUrl,\n      status,\n      timestamp: new Date().toISOString(),\n    });\n\n    // Update database: set isGenerating to false, update videoUrl, and set queue position to -1\n    try {\n      const updatedEntry = await db.entries.update({\n        where: { id: entryId },\n        data: {\n          isGenerating: false,\n          videoUrl: videoUrl,\n          queuePosition: -1, // Mark as completed\n          updatedAt: new Date(),\n        },\n      });\n\n      console.log(\"✅ Database updated for entry:\", entryId);\n\n      // Decrease all queue positions by 1 for entries that are not completed (-1)\n      await db.entries.updateMany({\n        where: {\n          queuePosition: {\n            gt: 0, // Greater than 0 (not completed)\n          },\n        },\n        data: {\n          queuePosition: {\n            decrement: 1,\n          },\n        },\n      });\n\n      console.log(\"✅ Queue positions updated - all entries moved up by 1\");\n\n      // Check if there's a new job at position 1 that needs to be started\n      const nextJob = await db.entries.findFirst({\n        where: {\n          queuePosition: 1,\n          isGenerating: true,\n        },\n        select: {\n          id: true,\n          scripts: true,\n          topicName: true,\n        },\n      });\n\n      if (nextJob) {\n        console.log(\"🚀 Starting next job in queue:\", nextJob.id);\n        await startBackendProcessing(\n          nextJob.id,\n          Array.isArray(nextJob.scripts) ? nextJob.scripts : [],\n          nextJob.topicName ?? \"\"\n        );\n      } else {\n        console.log(\"📋 No more jobs in queue\");\n      }\n\n      // Get user data for email\n      const user = await db.user.findFirst({\n        where: { id: updatedEntry.userId },\n      });\n\n      // Send completion email to user\n      if (user?.email) {\n        try {\n          await sendCompletionEmail(user.email, updatedEntry.prompt, videoUrl);\n          console.log(\"📧 Completion email sent to:\", user.email);\n        } catch (emailError) {\n          console.error(\"❌ Failed to send completion email:\", emailError);\n          // Don't fail the request if email fails\n        }\n      }\n\n      // Clean up media directory after successful completion\n      try {\n        await cleanupMediaDirectory();\n        console.log(\"🧹 Media directory cleaned up successfully\");\n      } catch (cleanupError) {\n        console.error(\"❌ Failed to cleanup media directory:\", cleanupError);\n        // Don't fail the request if cleanup fails\n      }\n\n      return NextResponse.json(\n        {\n          success: true,\n          message: \"Video completion processed successfully\",\n          entryId,\n          videoUrl,\n          status,\n        },\n        { status: 200 }\n      );\n    } catch (dbError) {\n      console.error(\"❌ Database update failed:\", dbError);\n      return NextResponse.json(\n        { error: \"Failed to update database\" },\n        { status: 500 }\n      );\n    }\n  } catch (error) {\n    console.error(\"❌ Error processing completion notification:\", error);\n    return NextResponse.json(\n      { error: \"Internal server error\" },\n      { status: 500 }\n    );\n  }\n}\n\nasync function startBackendProcessing(entryId: string, scripts: any[], topicName: string) {\n  try {\n    console.log(\"📤 Sending batch render request to backend for entry:\", entryId);\n\n    // Prepare batch render payload\n    const renderPayload = {\n      topicName: topicName,\n      entryId: entryId,\n      scripts: scripts.map((script) => ({\n        manim_code: script.manim_code,\n        description: script.description,\n      })),\n      priority: 0,\n    };\n\n    const response = await fetch(`${BACKEND_URL}/batch_render`, {\n      method: \"POST\",\n      headers: { \"Content-Type\": \"application/json\" },\n      body: JSON.stringify(renderPayload),\n    });\n\n    if (!response.ok) {\n      const errorText = await response.text();\n      console.error(`❌ Backend error: ${response.status} - ${errorText}`);\n      throw new Error(`Backend error: ${response.status} - ${errorText}`);\n    }\n\n    const result = await response.json();\n    console.log(\"✅ Backend processing started successfully:\", result);\n  } catch (error) {\n    console.error(\"❌ Failed to start backend processing:\", error);\n    // Note: We don't throw here to avoid breaking the completion flow\n    // The job will remain in queue and can be retried\n  }\n}\n\nasync function sendCompletionEmail(email: string, prompt: string, videoUrl: string) {\n  const emailHtml = `\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <meta charset=\"utf-8\">\n      <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n      <title>Your Video is Ready!</title>\n    </head>\n    <body style=\"margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh;\">\n      <div style=\"max-width: 600px; margin: 0 auto; padding: 40px 20px;\">\n        <div style=\"background: white; border-radius: 16px; padding: 40px; box-shadow: 0 20px 40px rgba(0,0,0,0.1);\">\n          <!-- Header -->\n          <div style=\"text-align: center; margin-bottom: 32px;\">\n            <div style=\"width: 80px; height: 80px; background: linear-gradient(135deg, #13a564 0%, #2089f9 100%); border-radius: 50%; margin: 0 auto 16px; display: flex; align-items: center; justify-content: center;\">\n              <span style=\"color: white; font-size: 32px;\">🎬</span>\n            </div>\n            <h1 style=\"color: #1a1a1a; font-size: 28px; font-weight: 700; margin: 0; line-height: 1.2;\">Your Video is Ready!</h1>\n          </div>\n\n          <!-- Content -->\n          <div style=\"margin-bottom: 32px;\">\n            <p style=\"color: #4a5568; font-size: 16px; line-height: 1.6; margin: 0 0 16px;\">\n              Great news! Your animated explanation video has been successfully generated and is ready to view.\n            </p>\n\n            <div style=\"background: #f7fafc; border-radius: 12px; padding: 20px; margin: 20px 0;\">\n              <h3 style=\"color: #2d3748; font-size: 16px; font-weight: 600; margin: 0 0 8px;\">Topic:</h3>\n              <p style=\"color: #4a5568; font-size: 14px; margin: 0; font-style: italic;\">\"${prompt}\"</p>\n            </div>\n\n            <p style=\"color: #4a5568; font-size: 16px; line-height: 1.6; margin: 16px 0;\">\n              Your video includes detailed animations, professional narration, and interactive quizzes to help you master the topic.\n            </p>\n          </div>\n\n          <!-- CTA Button -->\n          <div style=\"text-align: center; margin: 32px 0;\">\n            <a href=\"${process.env.NEXTAUTH_URL}/library\" style=\"display: inline-block; background: linear-gradient(135deg, #13a564 0%, #2089f9 100%); color: white; text-decoration: none; padding: 16px 32px; border-radius: 12px; font-weight: 600; font-size: 16px; box-shadow: 0 4px 12px rgba(19, 165, 100, 0.3);\">\n              View Your Video\n            </a>\n          </div>\n\n          <!-- Footer -->\n          <div style=\"text-align: center; padding-top: 24px; border-top: 1px solid #e2e8f0;\">\n            <p style=\"color: #718096; font-size: 14px; margin: 0;\">\n              Happy learning! 🚀<br>\n              The ClarifAI Team\n            </p>\n          </div>\n        </div>\n      </div>\n    </body>\n    </html>\n  `;\n\n  await resend.emails.send({\n    from: 'ClarifAI <<EMAIL>>',\n    to: [email],\n    subject: '🎉 Your Video is Ready!',\n    html: emailHtml,\n  });\n}\n\nasync function cleanupMediaDirectory() {\n  try {\n    // Path to the backend media directory\n    const mediaPath = path.join(process.cwd(), '..', 'backend', 'media');\n\n    // Check if media directory exists\n    try {\n      await fs.access(mediaPath);\n    } catch {\n      console.log(\"📁 Media directory doesn't exist, nothing to clean\");\n      return;\n    }\n\n    // Get all items in media directory\n    const items = await fs.readdir(mediaPath);\n\n    for (const item of items) {\n      const itemPath = path.join(mediaPath, item);\n      const stat = await fs.stat(itemPath);\n\n      if (stat.isDirectory()) {\n        // Remove directory and all its contents\n        await fs.rm(itemPath, { recursive: true, force: true });\n        console.log(`🗑️ Removed directory: ${item}`);\n      } else {\n        // Remove file\n        await fs.unlink(itemPath);\n        console.log(`🗑️ Removed file: ${item}`);\n      }\n    }\n\n    console.log(\"✅ Media directory completely cleaned\");\n  } catch (error) {\n    console.error(\"❌ Error cleaning media directory:\", error);\n    throw error;\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,MAAM,SAAS,IAAI,0IAAA,CAAA,SAAM,CAAC,QAAQ,GAAG,CAAC,eAAe;AACrD,MAAM,cAAc,2GAAuC;AAEpD,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,MAAM,QAAQ,IAAI;QAExD,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,QAAQ;YACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAyD,GAClE;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,GAAG,CAAC,kCAAkC;YAC5C;YACA;YACA;YACA,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,4FAA4F;QAC5F,IAAI;YACF,MAAM,eAAe,MAAM,kHAAA,CAAA,KAAE,CAAC,OAAO,CAAC,MAAM,CAAC;gBAC3C,OAAO;oBAAE,IAAI;gBAAQ;gBACrB,MAAM;oBACJ,cAAc;oBACd,UAAU;oBACV,eAAe,CAAC;oBAChB,WAAW,IAAI;gBACjB;YACF;YAEA,QAAQ,GAAG,CAAC,iCAAiC;YAE7C,4EAA4E;YAC5E,MAAM,kHAAA,CAAA,KAAE,CAAC,OAAO,CAAC,UAAU,CAAC;gBAC1B,OAAO;oBACL,eAAe;wBACb,IAAI;oBACN;gBACF;gBACA,MAAM;oBACJ,eAAe;wBACb,WAAW;oBACb;gBACF;YACF;YAEA,QAAQ,GAAG,CAAC;YAEZ,oEAAoE;YACpE,MAAM,UAAU,MAAM,kHAAA,CAAA,KAAE,CAAC,OAAO,CAAC,SAAS,CAAC;gBACzC,OAAO;oBACL,eAAe;oBACf,cAAc;gBAChB;gBACA,QAAQ;oBACN,IAAI;oBACJ,SAAS;oBACT,WAAW;gBACb;YACF;YAEA,IAAI,SAAS;gBACX,QAAQ,GAAG,CAAC,kCAAkC,QAAQ,EAAE;gBACxD,MAAM,uBACJ,QAAQ,EAAE,EACV,MAAM,OAAO,CAAC,QAAQ,OAAO,IAAI,QAAQ,OAAO,GAAG,EAAE,EACrD,QAAQ,SAAS,IAAI;YAEzB,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;YAEA,0BAA0B;YAC1B,MAAM,OAAO,MAAM,kHAAA,CAAA,KAAE,CAAC,IAAI,CAAC,SAAS,CAAC;gBACnC,OAAO;oBAAE,IAAI,aAAa,MAAM;gBAAC;YACnC;YAEA,gCAAgC;YAChC,IAAI,MAAM,OAAO;gBACf,IAAI;oBACF,MAAM,oBAAoB,KAAK,KAAK,EAAE,aAAa,MAAM,EAAE;oBAC3D,QAAQ,GAAG,CAAC,gCAAgC,KAAK,KAAK;gBACxD,EAAE,OAAO,YAAY;oBACnB,QAAQ,KAAK,CAAC,sCAAsC;gBACpD,wCAAwC;gBAC1C;YACF;YAEA,uDAAuD;YACvD,IAAI;gBACF,MAAM;gBACN,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,cAAc;gBACrB,QAAQ,KAAK,CAAC,wCAAwC;YACtD,0CAA0C;YAC5C;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,SAAS;gBACT;gBACA;gBACA;YACF,GACA;gBAAE,QAAQ;YAAI;QAElB,EAAE,OAAO,SAAS;YAChB,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA4B,GACrC;gBAAE,QAAQ;YAAI;QAElB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+CAA+C;QAC7D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,eAAe,uBAAuB,OAAe,EAAE,OAAc,EAAE,SAAiB;IACtF,IAAI;QACF,QAAQ,GAAG,CAAC,yDAAyD;QAErE,+BAA+B;QAC/B,MAAM,gBAAgB;YACpB,WAAW;YACX,SAAS;YACT,SAAS,QAAQ,GAAG,CAAC,CAAC,SAAW,CAAC;oBAChC,YAAY,OAAO,UAAU;oBAC7B,aAAa,OAAO,WAAW;gBACjC,CAAC;YACD,UAAU;QACZ;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,YAAY,aAAa,CAAC,EAAE;YAC1D,QAAQ;YACR,SAAS;gBAAE,gBAAgB;YAAmB;YAC9C,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,CAAC,iBAAiB,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,WAAW;YAClE,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,WAAW;QACpE;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,QAAQ,GAAG,CAAC,8CAA8C;IAC5D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;IACvD,kEAAkE;IAClE,kDAAkD;IACpD;AACF;AAEA,eAAe,oBAAoB,KAAa,EAAE,MAAc,EAAE,QAAgB;IAChF,MAAM,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;0FA2BqE,EAAE,OAAO;;;;;;;;;;qBAU9E,EAAE,QAAQ,GAAG,CAAC,YAAY,CAAC;;;;;;;;;;;;;;;;EAgB9C,CAAC;IAED,MAAM,OAAO,MAAM,CAAC,IAAI,CAAC;QACvB,MAAM;QACN,IAAI;YAAC;SAAM;QACX,SAAS;QACT,MAAM;IACR;AACF;AAEA,eAAe;IACb,IAAI;QACF,sCAAsC;QACtC,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,MAAM,WAAW;QAE5D,kCAAkC;QAClC,IAAI;YACF,MAAM,6FAAA,CAAA,WAAE,CAAC,MAAM,CAAC;QAClB,EAAE,OAAM;YACN,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,mCAAmC;QACnC,MAAM,QAAQ,MAAM,6FAAA,CAAA,WAAE,CAAC,OAAO,CAAC;QAE/B,KAAK,MAAM,QAAQ,MAAO;YACxB,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,WAAW;YACtC,MAAM,OAAO,MAAM,6FAAA,CAAA,WAAE,CAAC,IAAI,CAAC;YAE3B,IAAI,KAAK,WAAW,IAAI;gBACtB,wCAAwC;gBACxC,MAAM,6FAAA,CAAA,WAAE,CAAC,EAAE,CAAC,UAAU;oBAAE,WAAW;oBAAM,OAAO;gBAAK;gBACrD,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,MAAM;YAC9C,OAAO;gBACL,cAAc;gBACd,MAAM,6FAAA,CAAA,WAAE,CAAC,MAAM,CAAC;gBAChB,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,MAAM;YACzC;QACF;QAEA,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM;IACR;AACF", "debugId": null}}]}