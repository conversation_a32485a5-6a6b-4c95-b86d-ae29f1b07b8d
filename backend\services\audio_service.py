import os
import tempfile
import subprocess
import logging
import requests
from typing import Optional
from dotenv import load_dotenv
from config.queue_config import get_queue_config
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.retry_utils import infinite_retry, ensure_directory_exists, safe_file_operation

# Load environment variables like x.py does
dotenv_path = os.path.join(os.path.dirname(__file__), "..", ".env")
load_dotenv(dotenv_path=dotenv_path)

logger = logging.getLogger(__name__)


class AudioService:
    """Service for text-to-speech generation and audio processing."""

    def __init__(self):
        self.config = get_queue_config()
        self.audio_dir = "media/audio"
        ensure_directory_exists(self.audio_dir)

    def generate_tts_audio(self, text: str) -> str:
        """
        Generate TTS audio from text with infinite retries.

        Args:
            text: Text to convert to speech

        Returns:
            Path to generated audio file (guaranteed success)
        """
        @infinite_retry(operation_name="TTS audio generation")
        def _generate_tts():
            tts_endpoint = os.getenv("TTS_ENDPOINT")
            if not tts_endpoint:
                raise ValueError("TTS_ENDPOINT environment variable not set")

            # Ensure audio directory exists
            ensure_directory_exists(self.audio_dir)

            headers = {"Content-Type": "application/json"}
            temp_audio = tempfile.NamedTemporaryFile(
                suffix=".mp3",
                dir=self.audio_dir,
                delete=False
            )

            try:
                response = requests.post(
                    tts_endpoint,
                    headers=headers,
                    json={"text": text},
                    timeout=60
                )
                response.raise_for_status()

                if not response.content:
                    raise ValueError("Empty response from TTS endpoint")

                temp_audio.write(response.content)
                temp_audio.flush()
                temp_audio.close()

                # Verify file was created and has content
                if not os.path.exists(temp_audio.name):
                    raise FileNotFoundError(f"TTS audio file not created: {temp_audio.name}")

                if os.path.getsize(temp_audio.name) == 0:
                    os.remove(temp_audio.name)
                    raise ValueError("TTS audio file is empty")

                logger.info(f"🔊 TTS audio ready: {temp_audio.name}")
                return temp_audio.name

            except Exception as e:
                # Clean up temp file on failure
                try:
                    temp_audio.close()
                    if os.path.exists(temp_audio.name):
                        os.remove(temp_audio.name)
                except:
                    pass
                raise e

        return _generate_tts()

    def adjust_audio_tempo(self, audio_path: str, target_duration: float, video_id: str) -> str:
        """
        Adjust audio tempo to match target duration with infinite retries.

        Args:
            audio_path: Path to input audio file
            target_duration: Target duration in seconds
            video_id: Unique identifier for output file

        Returns:
            Path to adjusted audio file (guaranteed success)
        """
        @infinite_retry(operation_name=f"audio tempo adjustment for {video_id}")
        def _adjust_tempo():
            # Verify input file exists
            if not os.path.exists(audio_path):
                raise FileNotFoundError(f"Input audio file not found: {audio_path}")

            # Get current audio duration
            audio_duration = self._get_audio_duration(audio_path)
            if audio_duration <= 0:
                raise ValueError(f"Invalid audio duration: {audio_duration}")

            # Calculate tempo ratio
            if audio_duration > target_duration:
                tempo_ratio = audio_duration / target_duration
            else:
                tempo_ratio = target_duration / audio_duration

            # Clamp tempo ratio to reasonable bounds
            tempo_ratio = max(0.5, min(2.0, tempo_ratio))

            logger.info(f"Adjusting audio tempo by {tempo_ratio}x")

            # Ensure output directory exists
            ensure_directory_exists(self.audio_dir)

            # Create output path
            adjusted_audio = os.path.join(self.audio_dir, f"{video_id}_adjusted.aac")

            # Remove existing output file if it exists
            if os.path.exists(adjusted_audio):
                os.remove(adjusted_audio)

            # Run ffmpeg to adjust tempo
            cmd = [
                "ffmpeg", "-y",
                "-i", audio_path,
                "-filter:a", f"atempo={tempo_ratio}",
                "-vn",
                "-acodec", "aac",
                adjusted_audio,
            ]

            subprocess.run(cmd, check=True, capture_output=True, text=True)

            # Verify output file was created
            if not os.path.exists(adjusted_audio):
                raise FileNotFoundError(f"Adjusted audio file not created: {adjusted_audio}")

            if os.path.getsize(adjusted_audio) == 0:
                raise ValueError("Adjusted audio file is empty")

            logger.info(f"Audio tempo adjusted: {adjusted_audio}")
            return adjusted_audio

        return _adjust_tempo()

    def merge_audio_video(self, video_path: str, audio_path: str, video_id: str) -> str:
        """
        Merge audio and video files with infinite retries.

        Args:
            video_path: Path to video file
            audio_path: Path to audio file
            video_id: Unique identifier for output

        Returns:
            Path to merged video file (guaranteed success)
        """
        @infinite_retry(operation_name=f"audio/video merge for {video_id}")
        def _merge_audio_video():
            # Verify input files exist
            if not os.path.exists(video_path):
                raise FileNotFoundError(f"Video file not found: {video_path}")
            if not os.path.exists(audio_path):
                raise FileNotFoundError(f"Audio file not found: {audio_path}")

            result_dir = "media/result"
            ensure_directory_exists(result_dir)

            stretched_audio = os.path.join(self.audio_dir, f"{video_id}_adjusted.aac")
            final_output = os.path.join(result_dir, f"{video_id}_final.mp4")

            # Clean up any existing output files
            for file_path in [stretched_audio, final_output]:
                if os.path.exists(file_path):
                    os.remove(file_path)

            video_duration = self._get_media_duration(video_path)
            audio_duration = self._get_media_duration(audio_path)

            if audio_duration <= 0 or video_duration <= 0:
                raise ValueError(f"Invalid durations - video: {video_duration}s, audio: {audio_duration}s")

            if audio_duration > video_duration:
                tempo_ratio = int(audio_duration / video_duration)
            else:
                tempo_ratio = int(video_duration / audio_duration)

            logger.info(f"🎚️ Adjusting audio tempo: {tempo_ratio}x")

            # Step 1: Adjust audio tempo
            subprocess.run(
                [
                    "ffmpeg",
                    "-y",
                    "-i",
                    audio_path,
                    "-filter:a",
                    f"atempo={tempo_ratio}",
                    "-vn",
                    "-acodec",
                    "aac",
                    stretched_audio,
                ],
                check=True,
                capture_output=True,
                text=True
            )

            # Verify stretched audio was created
            if not os.path.exists(stretched_audio):
                raise FileNotFoundError(f"Stretched audio file not created: {stretched_audio}")

            # Step 2: Merge video and adjusted audio
            subprocess.run(
                [
                    "ffmpeg",
                    "-y",
                    "-i",
                    video_path,
                    "-i",
                    stretched_audio,
                    "-map",
                    "0:v:0",
                    "-map",
                    "1:a:0",
                    "-c:v",
                    "copy",
                    "-c:a",
                    "aac",
                    "-shortest",
                    final_output,
                ],
                check=True,
                capture_output=True,
                text=True
            )

            # Verify final output was created
            if not os.path.exists(final_output):
                raise FileNotFoundError(f"Final merged video not created: {final_output}")

            if os.path.getsize(final_output) == 0:
                raise ValueError("Final merged video is empty")

            logger.info(f"✅ Final video saved at: {final_output}")

            # Clean up intermediate files
            try:
                os.remove(audio_path)
                os.remove(stretched_audio)
            except Exception as e:
                logger.warning(f"⚠️ Failed to clean up intermediate files: {e}")

            return final_output

        return _merge_audio_video()

    def _get_audio_duration(self, audio_path: str) -> float:
        """Get audio file duration using ffprobe."""
        return self._get_media_duration(audio_path)

    def _get_video_duration(self, video_path: str) -> float:
        """Get video file duration using ffprobe."""
        return self._get_media_duration(video_path)

    def _get_media_duration(self, path: str) -> float:
        """Get media file duration using ffprobe."""
        try:
            result = subprocess.run(
                [
                    "ffprobe",
                    "-v", "error",
                    "-show_entries", "format=duration",
                    "-of", "default=noprint_wrappers=1:nokey=1",
                    path,
                ],
                capture_output=True,
                text=True,
                check=True,
            )
            return float(result.stdout.strip())
        except Exception as e:
            logger.warning(f"Could not get duration for {path}: {e}")
            return 0.0

    def cleanup_audio_files(self, *audio_paths):
        """Clean up audio files."""
        for path in audio_paths:
            if path and os.path.exists(path):
                try:
                    os.remove(path)
                except Exception as e:
                    logger.warning(f"Failed to remove audio file {path}: {e}")


# Global service instance
_audio_service: Optional[AudioService] = None


def get_audio_service() -> AudioService:
    """Get the global AudioService instance."""
    global _audio_service
    if _audio_service is None:
        _audio_service = AudioService()
    return _audio_service
